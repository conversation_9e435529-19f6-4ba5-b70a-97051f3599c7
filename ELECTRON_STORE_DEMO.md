# Electron Store 演示项目

这个项目演示了如何在 Electron + React + TypeScript 应用中使用 `electron-store` 进行本地数据存储。

## 项目特性

- ✅ **完整的 CRUD 操作**: 创建、读取、更新、删除存储数据
- ✅ **多种数据类型支持**: 字符串、JSON 对象、数组、数字、布尔值
- ✅ **类型安全**: 完整的 TypeScript 类型定义
- ✅ **错误处理**: 完善的错误捕获和用户反馈
- ✅ **用户友好界面**: 直观的操作界面和实时反馈
- ✅ **数据持久化**: 数据自动保存到本地文件系统

## 安装的依赖

```bash
yarn add electron-store
```

## 项目结构

```
src/
├── main/
│   └── index.ts                 # 主进程，包含 electron-store 的 IPC 处理
├── preload/
│   ├── index.ts                 # 预加载脚本，暴露 store API
│   └── index.d.ts               # 类型定义文件
└── renderer/src/
    ├── components/
    │   ├── ElectronStoreDemo.tsx    # 演示组件
    │   └── ElectronStoreDemo.css    # 样式文件
    └── App.tsx                      # 主应用组件
```

## 核心实现

### 1. 主进程 (src/main/index.ts)

```typescript
// 动态导入 electron-store
const Store = (await import('electron-store')).default
store = new Store()

// IPC 处理器
ipcMain.handle('store-get', (_, key: string) => store.get(key))
ipcMain.handle('store-set', (_, key: string, value: unknown) => {
  store.set(key, value)
  return true
})
ipcMain.handle('store-delete', (_, key: string) => {
  store.delete(key)
  return true
})
// ... 更多处理器
```

### 2. 预加载脚本 (src/preload/index.ts)

```typescript
const api = {
  store: {
    get: (key: string) => ipcRenderer.invoke('store-get', key),
    set: (key: string, value: unknown) => ipcRenderer.invoke('store-set', key, value),
    delete: (key: string) => ipcRenderer.invoke('store-delete', key),
    clear: () => ipcRenderer.invoke('store-clear'),
    has: (key: string) => ipcRenderer.invoke('store-has', key),
    getAll: () => ipcRenderer.invoke('store-get-all'),
    size: () => ipcRenderer.invoke('store-size')
  }
}
```

### 3. 渲染进程组件 (src/renderer/src/components/ElectronStoreDemo.tsx)

```typescript
// 保存数据
const handleSave = async () => {
  try {
    let parsedValue: unknown = value
    try {
      parsedValue = JSON.parse(value)
    } catch {
      parsedValue = value
    }
    await window.api.store.set(key, parsedValue)
    showMessage(`成功保存: ${key}`, 'success')
  } catch (error) {
    showMessage('保存失败: ' + (error as Error).message, 'error')
  }
}
```

## 功能说明

### 数据存储
- 支持键值对存储
- 自动检测和解析 JSON 格式数据
- 支持字符串、对象、数组、数字、布尔值等类型

### 数据读取
- 获取单个键的值
- 获取所有存储的数据
- 检查键是否存在
- 获取存储项目总数

### 数据管理
- 删除单个存储项
- 清空所有存储数据
- 实时更新数据列表

### 用户体验
- 实时消息反馈（成功/错误/信息）
- 响应式界面设计
- 数据格式化显示
- 确认对话框防止误操作

## 使用方法

1. **启动应用**:
   ```bash
   yarn dev
   ```

2. **访问演示页面**:
   - 在首页点击 "Electron Store 演示" 链接

3. **基本操作**:
   - **保存数据**: 输入键名和值，点击"保存"
   - **获取数据**: 输入键名，点击"获取"
   - **检查存在**: 输入键名，点击"检查存在"
   - **删除数据**: 点击数据项旁的"删除"按钮
   - **清空所有**: 点击"清空所有"按钮

4. **数据格式示例**:
   ```javascript
   // 字符串
   "Hello World"
   
   // JSON 对象
   {"name": "张三", "age": 25, "city": "北京"}
   
   // 数组
   ["苹果", "香蕉", "橙子"]
   
   // 数字
   42
   
   // 布尔值
   true
   ```

## 数据存储位置

electron-store 会将数据保存在操作系统的标准应用数据目录中：

- **Windows**: `%APPDATA%\stock\config.json`
- **macOS**: `~/Library/Preferences/stock/config.json`
- **Linux**: `~/.config/stock/config.json`

## 技术要点

1. **安全性**: 使用 contextBridge 安全地暴露 API 给渲染进程
2. **类型安全**: 完整的 TypeScript 类型定义
3. **错误处理**: 完善的异常捕获和用户反馈
4. **性能**: 异步操作避免阻塞 UI
5. **用户体验**: 直观的界面和实时反馈

## 扩展建议

1. **数据验证**: 添加数据格式验证
2. **数据导入导出**: 支持 JSON 文件导入导出
3. **搜索功能**: 添加数据搜索和过滤
4. **数据备份**: 实现数据备份和恢复功能
5. **加密存储**: 对敏感数据进行加密存储

这个演示项目提供了一个完整的 electron-store 使用示例，可以作为实际项目开发的参考基础。
