# Electron Store 演示测试指南

## 功能测试清单

### 1. 基本存储功能
- [ ] 输入键名和值，点击"保存"按钮
- [ ] 验证数据是否出现在下方的"存储的数据"列表中
- [ ] 验证存储项目数量是否正确更新

### 2. 数据类型支持
测试以下不同类型的数据：

#### 字符串数据
- 键名: `username`
- 值: `张三`

#### JSON 对象数据
- 键名: `user_info`
- 值: `{"name": "李四", "age": 25, "city": "北京"}`

#### 数组数据
- 键名: `hobbies`
- 值: `["读书", "游泳", "编程"]`

#### 数字数据
- 键名: `score`
- 值: `95.5`

#### 布尔值数据
- 键名: `is_active`
- 值: `true`

### 3. 数据读取功能
- [ ] 输入已存在的键名，点击"获取"按钮
- [ ] 验证值是否正确显示在值输入框中
- [ ] 测试获取不存在的键，应显示错误消息

### 4. 数据检查功能
- [ ] 输入已存在的键名，点击"检查存在"按钮
- [ ] 验证显示"键存在"的消息
- [ ] 输入不存在的键名，验证显示"键不存在"的消息

### 5. 数据删除功能
- [ ] 点击某个存储项的"删除"按钮
- [ ] 验证该项是否从列表中移除
- [ ] 验证存储项目数量是否正确更新

### 6. 清空所有数据功能
- [ ] 点击"清空所有"按钮
- [ ] 确认对话框中点击"确定"
- [ ] 验证所有数据是否被清空
- [ ] 验证存储项目数量是否变为 0

### 7. 错误处理测试
- [ ] 尝试保存空键名，应显示错误消息
- [ ] 尝试获取空键名，应显示错误消息
- [ ] 尝试检查空键名，应显示错误消息

### 8. 用户界面测试
- [ ] 验证消息提示是否正确显示（成功、错误、信息）
- [ ] 验证消息是否在 3 秒后自动消失
- [ ] 验证按钮点击效果和样式
- [ ] 验证响应式设计在不同窗口大小下的表现

### 9. 数据持久性测试
- [ ] 保存一些数据
- [ ] 关闭应用程序
- [ ] 重新启动应用程序
- [ ] 验证之前保存的数据是否仍然存在

## 预期结果

1. **数据存储**: 所有类型的数据都应该能够正确保存和显示
2. **数据读取**: 能够正确获取已保存的数据
3. **数据删除**: 能够删除单个项目或清空所有数据
4. **错误处理**: 对于无效操作应显示适当的错误消息
5. **用户体验**: 界面响应迅速，消息提示清晰
6. **数据持久性**: 数据在应用重启后仍然保持

## 使用说明

1. 启动应用程序后，在首页点击"Electron Store 演示"链接
2. 进入演示页面后，可以看到输入区域和数据展示区域
3. 在键名输入框中输入要保存的键
4. 在值输入框中输入要保存的值（支持 JSON 格式）
5. 点击相应的按钮执行操作
6. 观察页面上的反馈消息和数据变化

## 技术特性

- **类型安全**: 使用 TypeScript 确保类型安全
- **错误处理**: 完善的错误捕获和用户反馈
- **用户体验**: 直观的界面设计和操作反馈
- **数据格式**: 支持字符串、JSON 对象、数组、数字、布尔值等多种数据类型
- **持久化**: 数据自动保存到本地文件系统
