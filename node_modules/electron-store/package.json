{"name": "electron-store", "version": "10.1.0", "description": "Simple data persistence for your Electron app or module - Save and load user settings, app state, cache, etc", "license": "MIT", "repository": "sindresorhus/electron-store", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=20"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["electron", "store", "app", "config", "storage", "conf", "configuration", "settings", "preferences", "json", "data", "persist", "persistent", "save"], "dependencies": {"conf": "^14.0.0", "type-fest": "^4.41.0"}, "devDependencies": {"ava": "^6.4.0", "electron": "^31.0.1", "execa": "^9.6.0", "tsd": "^0.32.0", "xo": "^0.58.0"}, "xo": {"envs": ["node", "browser"]}, "tsd": {"compilerOptions": {"module": "node16", "moduleResolution": "node16", "moduleDetection": "force"}}}