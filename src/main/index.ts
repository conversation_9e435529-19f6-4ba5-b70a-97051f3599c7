import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'

// 全局变量存储 store 实例
let store: any

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 900,
    height: 670,
    show: false,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // 动态导入 electron-store
  const Store = (await import('electron-store')).default
  store = new Store({
    // 默认值 - 当键不存在时返回这些值
    defaults: {
      testKey: 111,
      appName: 'Electron Store Demo',
      version: '1.0.0',
      settings: {
        theme: 'light',
        language: 'zh-CN'
      },
      userPreferences: {
        autoSave: true,
        notifications: true
      }
    },
    // 可选：自定义配置文件名
    name: 'app-config',
    // 可选：文件扩展名
    fileExtension: 'json',
    // 可选：清除无效的配置项
    clearInvalidConfig: true
  })

  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // IPC test
  ipcMain.on('ping', () => console.log('pong'))

  // electron-store IPC handlers
  ipcMain.handle('store-get', (_, key: string) => {
    return store.get(key)
  })

  ipcMain.handle('store-set', (_, key: string, value: unknown) => {
    store.set(key, value)
    return true
  })

  ipcMain.handle('store-delete', (_, key: string) => {
    store.delete(key)
    return true
  })

  ipcMain.handle('store-clear', () => {
    store.clear()
    return true
  })

  ipcMain.handle('store-has', (_, key: string) => {
    return store.has(key)
  })

  ipcMain.handle('store-get-all', () => {
    return store.store
  })

  ipcMain.handle('store-size', () => {
    return store.size
  })

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
