import { useState } from 'react'
import Versions from './components/Versions'
import ElectronStoreDemo from './components/ElectronStoreDemo'
import electronLogo from './assets/electron.svg'

function App(): React.JSX.Element {
  const [currentView, setCurrentView] = useState<'home' | 'store'>('home')
  const ipcHandle = (): void => window.electron.ipcRenderer.send('ping')

  if (currentView === 'store') {
    return (
      <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
        <div style={{
          padding: '10px',
          textAlign: 'center',
          borderBottom: '1px solid #eee',
          flexShrink: 0,
          backgroundColor: '#f8f9fa'
        }}>
          <button
            onClick={() => setCurrentView('home')}
            style={{
              padding: '8px 16px',
              backgroundColor: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            返回首页
          </button>
        </div>
        <div style={{ flex: 1, overflow: 'hidden' }}>
          <ElectronStoreDemo />
        </div>
      </div>
    )
  }

  return (
    <>
      <img alt="logo" className="logo" src={electronLogo} />
      <div className="creator">Powered by electron-vite</div>
      <div className="text">
        Build an Electron app with <span className="react">React</span>
        &nbsp;and <span className="ts">TypeScript</span>
      </div>
      <p className="tip">
        Please try pressing <code>F12</code> to open the devTool
      </p>
      <div className="actions">
        <div className="action">
          <a href="https://electron-vite.org/" target="_blank" rel="noreferrer">
            Documentation
          </a>
        </div>
        <div className="action">
          <a target="_blank" rel="noreferrer" onClick={ipcHandle}>
            Send IPC
          </a>
        </div>
        <div className="action">
          <a
            href="#"
            onClick={(e) => {
              e.preventDefault()
              setCurrentView('store')
            }}
          >
            Electron Store 演示
          </a>
        </div>
      </div>
      <Versions></Versions>
    </>
  )
}

export default App
