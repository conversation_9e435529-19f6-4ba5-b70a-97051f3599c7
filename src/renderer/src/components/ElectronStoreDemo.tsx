import React, { useState, useEffect } from 'react'
import './ElectronStoreDemo.css'

interface StoredItem {
  key: string
  value: unknown
}

const ElectronStoreDemo: React.FC = () => {
  const [key, setKey] = useState<string>('')
  const [value, setValue] = useState<string>('')
  const [storedItems, setStoredItems] = useState<StoredItem[]>([])
  const [message, setMessage] = useState<string>('')
  const [messageType, setMessageType] = useState<'success' | 'error' | 'info'>('info')
  const [storeSize, setStoreSize] = useState<number>(0)

  // 显示消息的辅助函数
  const showMessage = (msg: string, type: 'success' | 'error' | 'info' = 'info') => {
    setMessage(msg)
    setMessageType(type)
    setTimeout(() => setMessage(''), 3000)
  }

  // 加载所有存储的数据
  const loadStoredData = async () => {
    try {
      const allData = await window.api.store.getAll()
      const items: StoredItem[] = Object.entries(allData).map(([key, value]) => ({
        key,
        value
      }))
      setStoredItems(items)
      
      const size = await window.api.store.size()
      setStoreSize(size)
    } catch (error) {
      showMessage('加载数据失败: ' + (error as Error).message, 'error')
    }
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadStoredData()
  }, [])

  // 保存数据
  const handleSave = async () => {
    if (!key.trim()) {
      showMessage('请输入键名', 'error')
      return
    }

    try {
      // 尝试解析 JSON，如果失败则作为字符串保存
      let parsedValue: unknown = value
      try {
        parsedValue = JSON.parse(value)
      } catch {
        // 如果不是有效的 JSON，保持为字符串
        parsedValue = value
      }

      await window.api.store.set(key, parsedValue)
      showMessage(`成功保存: ${key}`, 'success')
      setKey('')
      setValue('')
      await loadStoredData()
    } catch (error) {
      showMessage('保存失败: ' + (error as Error).message, 'error')
    }
  }

  // 删除单个项目
  const handleDelete = async (keyToDelete: string) => {
    try {
      await window.api.store.delete(keyToDelete)
      showMessage(`成功删除: ${keyToDelete}`, 'success')
      await loadStoredData()
    } catch (error) {
      showMessage('删除失败: ' + (error as Error).message, 'error')
    }
  }

  // 清空所有数据
  const handleClearAll = async () => {
    if (!window.confirm('确定要清空所有存储的数据吗？')) {
      return
    }

    try {
      await window.api.store.clear()
      showMessage('已清空所有数据', 'success')
      await loadStoredData()
    } catch (error) {
      showMessage('清空失败: ' + (error as Error).message, 'error')
    }
  }

  // 检查键是否存在
  const handleCheckExists = async () => {
    if (!key.trim()) {
      showMessage('请输入要检查的键名', 'error')
      return
    }

    try {
      const exists = await window.api.store.has(key)
      showMessage(`键 "${key}" ${exists ? '存在' : '不存在'}`, 'info')
    } catch (error) {
      showMessage('检查失败: ' + (error as Error).message, 'error')
    }
  }

  // 获取单个值
  const handleGet = async () => {
    if (!key.trim()) {
      showMessage('请输入要获取的键名', 'error')
      return
    }

    try {
      const retrievedValue = await window.api.store.get(key)
      if (retrievedValue !== undefined) {
        setValue(typeof retrievedValue === 'string' ? retrievedValue : JSON.stringify(retrievedValue, null, 2))
        showMessage(`成功获取键 "${key}" 的值`, 'success')
      } else {
        showMessage(`键 "${key}" 不存在`, 'error')
      }
    } catch (error) {
      showMessage('获取失败: ' + (error as Error).message, 'error')
    }
  }

  // 格式化显示值
  const formatValue = (value: unknown): string => {
    if (typeof value === 'string') {
      return value
    }
    return JSON.stringify(value, null, 2)
  }

  return (
    <div className="electron-store-demo">
      <h1>Electron Store 演示</h1>
      
      {/* 消息显示区域 */}
      {message && (
        <div className={`message message-${messageType}`}>
          {message}
        </div>
      )}

      {/* 统计信息 */}
      <div className="stats">
        <span>存储项目数量: {storeSize}</span>
      </div>

      {/* 操作区域 */}
      <div className="controls">
        <div className="input-group">
          <label htmlFor="key-input">键名:</label>
          <input
            id="key-input"
            type="text"
            value={key}
            onChange={(e) => setKey(e.target.value)}
            placeholder="输入键名"
          />
        </div>

        <div className="input-group">
          <label htmlFor="value-input">值 (支持 JSON):</label>
          <textarea
            id="value-input"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            placeholder='输入值，例如: "hello" 或 {"name": "张三", "age": 25}'
            rows={3}
          />
        </div>

        <div className="button-group">
          <button onClick={handleSave} className="btn btn-primary">
            保存
          </button>
          <button onClick={handleGet} className="btn btn-secondary">
            获取
          </button>
          <button onClick={handleCheckExists} className="btn btn-info">
            检查存在
          </button>
          <button onClick={handleClearAll} className="btn btn-danger">
            清空所有
          </button>
        </div>
      </div>

      {/* 数据展示区域 */}
      <div className="data-display">
        <h2>存储的数据</h2>
        {storedItems.length === 0 ? (
          <p className="no-data">暂无存储数据</p>
        ) : (
          <div className="items-list">
            {storedItems.map((item) => (
              <div key={item.key} className="item">
                <div className="item-header">
                  <strong>{item.key}</strong>
                  <button
                    onClick={() => handleDelete(item.key)}
                    className="btn btn-small btn-danger"
                  >
                    删除
                  </button>
                </div>
                <div className="item-value">
                  <pre>{formatValue(item.value)}</pre>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default ElectronStoreDemo
