import { ElectronAPI } from '@electron-toolkit/preload'

// 定义 store API 的类型
interface StoreAPI {
  get: (key: string) => Promise<unknown>
  set: (key: string, value: unknown) => Promise<boolean>
  delete: (key: string) => Promise<boolean>
  clear: () => Promise<boolean>
  has: (key: string) => Promise<boolean>
  getAll: () => Promise<Record<string, unknown>>
  size: () => Promise<number>
}

declare global {
  interface Window {
    electron: ElectronAPI
    api: {
      store: StoreAPI
    }
  }
}
