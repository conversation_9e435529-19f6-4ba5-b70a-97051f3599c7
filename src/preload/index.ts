import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

// Custom APIs for renderer
const api = {
  store: {
    get: (key: string) => ipcRenderer.invoke('store-get', key),
    set: (key: string, value: unknown) => ipc<PERSON>enderer.invoke('store-set', key, value),
    delete: (key: string) => ipc<PERSON>enderer.invoke('store-delete', key),
    clear: () => ipc<PERSON>enderer.invoke('store-clear'),
    has: (key: string) => ipcRenderer.invoke('store-has', key),
    getAll: () => ipc<PERSON>enderer.invoke('store-get-all'),
    size: () => ipcRenderer.invoke('store-size')
  }
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}
