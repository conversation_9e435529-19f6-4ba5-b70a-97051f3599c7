"use strict";
const electron = require("electron");
const preload = require("@electron-toolkit/preload");
const api = {
  store: {
    get: (key) => electron.ipcRenderer.invoke("store-get", key),
    set: (key, value) => electron.ipcRenderer.invoke("store-set", key, value),
    delete: (key) => electron.ipcRenderer.invoke("store-delete", key),
    clear: () => electron.ipcRenderer.invoke("store-clear"),
    has: (key) => electron.ipcRenderer.invoke("store-has", key),
    getAll: () => electron.ipcRenderer.invoke("store-get-all"),
    size: () => electron.ipcRenderer.invoke("store-size")
  }
};
if (process.contextIsolated) {
  try {
    electron.contextBridge.exposeInMainWorld("electron", preload.electronAPI);
    electron.contextBridge.exposeInMainWorld("api", api);
  } catch (error) {
    console.error(error);
  }
} else {
  window.electron = preload.electronAPI;
  window.api = api;
}
