# Electron Store 配置选项详解

## 当前配置

```typescript
store = new Store({
  // 默认值 - 当键不存在时返回这些值
  defaults: {
    testKey: 111,
    appName: 'Electron Store Demo',
    version: '1.0.0',
    settings: {
      theme: 'light',
      language: 'zh-CN'
    },
    userPreferences: {
      autoSave: true,
      notifications: true
    }
  },
  // 可选：自定义配置文件名
  name: 'app-config',
  // 可选：文件扩展名
  fileExtension: 'json',
  // 可选：清除无效的配置项
  clearInvalidConfig: true
})
```

## 配置选项说明

### 1. `defaults` - 默认值
设置默认值，当获取不存在的键时会返回这些默认值。

```typescript
defaults: {
  testKey: 111,                    // 数字类型
  appName: 'Electron Store Demo',  // 字符串类型
  settings: {                      // 对象类型
    theme: 'light',
    language: 'zh-CN'
  },
  userPreferences: {               // 嵌套对象
    autoSave: true,                // 布尔类型
    notifications: true
  }
}
```

### 2. `name` - 配置文件名
自定义配置文件的名称，默认为 `config`。

```typescript
name: 'app-config'  // 文件将保存为 app-config.json
```

### 3. `fileExtension` - 文件扩展名
设置配置文件的扩展名，默认为 `json`。

```typescript
fileExtension: 'json'  // 也可以设置为其他扩展名如 'conf'
```

### 4. `clearInvalidConfig` - 清除无效配置
当配置文件损坏时是否自动清除，默认为 `false`。

```typescript
clearInvalidConfig: true  // 推荐设置为 true
```

## 其他可用配置选项

### 5. `cwd` - 自定义存储目录
```typescript
cwd: '/path/to/custom/directory'  // 自定义存储路径
```

### 6. `encryptionKey` - 加密密钥
```typescript
encryptionKey: 'my-secret-key'  // 对存储的数据进行加密
```

### 7. `serialize` 和 `deserialize` - 自定义序列化
```typescript
serialize: (value) => JSON.stringify(value, null, 2),
deserialize: (text) => JSON.parse(text)
```

### 8. `projectSuffix` - 项目后缀
```typescript
projectSuffix: 'dev'  // 在开发环境中使用不同的配置文件
```

### 9. `schema` - 数据验证模式
```typescript
schema: {
  testKey: {
    type: 'number',
    minimum: 0,
    maximum: 1000
  },
  appName: {
    type: 'string',
    minLength: 1
  }
}
```

## 实际使用示例

### 基础配置
```typescript
const store = new Store({
  defaults: {
    windowBounds: { width: 800, height: 600 },
    theme: 'light'
  }
})
```

### 开发环境配置
```typescript
const store = new Store({
  name: 'app-config',
  projectSuffix: process.env.NODE_ENV === 'development' ? 'dev' : '',
  defaults: {
    apiUrl: process.env.NODE_ENV === 'development' 
      ? 'http://localhost:3000' 
      : 'https://api.example.com'
  }
})
```

### 加密存储配置
```typescript
const store = new Store({
  encryptionKey: 'your-secret-key',
  defaults: {
    userToken: null,
    sensitiveData: {}
  }
})
```

## 存储位置

根据当前配置，文件将保存在：

- **Windows**: `%APPDATA%\stock\app-config.json`
- **macOS**: `~/Library/Preferences/stock/app-config.json`
- **Linux**: `~/.config/stock/app-config.json`

## 默认值的优势

1. **初始化数据**: 应用首次运行时就有可用的默认配置
2. **向后兼容**: 添加新配置项时，旧版本用户也能获得合理的默认值
3. **错误恢复**: 当配置文件损坏时，可以回退到默认值
4. **开发便利**: 开发时不需要手动创建初始配置

## 测试默认值

启动应用后，您可以在演示页面中看到以下预设的默认值：

- `testKey`: 111
- `appName`: "Electron Store Demo"
- `version`: "1.0.0"
- `settings`: {"theme": "light", "language": "zh-CN"}
- `userPreferences`: {"autoSave": true, "notifications": true}

这些值在应用首次启动时就会存在，您可以直接获取或修改它们。

## 最佳实践

1. **合理设置默认值**: 确保默认值是安全和有意义的
2. **使用类型化配置**: 结合 TypeScript 接口定义配置结构
3. **环境区分**: 在不同环境中使用不同的配置文件
4. **敏感数据加密**: 对包含敏感信息的配置使用加密
5. **配置验证**: 使用 schema 验证配置数据的有效性

这样的配置使得您的应用在首次运行时就有完整的配置数据，提供更好的用户体验。
