# 快速测试指南 - Electron Store 演示

## 🚀 立即测试滚动和预设数据

### 步骤 1: 进入演示页面
1. 启动应用后，点击首页的 "Electron Store 演示" 链接
2. 现在页面应该可以正常滚动了！

### 步骤 2: 查看预设的默认数据
页面加载后，您应该能在"存储的数据"区域看到以下预设数据：

```
testKey: 111
appName: "Electron Store Demo"
version: "1.0.0"
settings: {
  "theme": "light",
  "language": "zh-CN"
}
userPreferences: {
  "autoSave": true,
  "notifications": true
}
```

### 步骤 3: 测试滚动功能
- 使用鼠标滚轮或触控板滚动页面
- 应该能看到完整的界面，包括：
  - 顶部的标题和统计信息
  - 中间的操作控制区域
  - 底部的数据展示区域
- 滚动条应该显示在右侧（如果内容超出视窗）

### 步骤 4: 快速功能测试

#### 测试获取预设数据
1. 在键名输入框中输入：`testKey`
2. 点击"获取"按钮
3. 应该在值输入框中看到：`111`

#### 测试获取对象数据
1. 在键名输入框中输入：`settings`
2. 点击"获取"按钮
3. 应该在值输入框中看到格式化的 JSON：
   ```json
   {
     "theme": "light",
     "language": "zh-CN"
   }
   ```

#### 测试保存新数据
1. 在键名输入框中输入：`myTest`
2. 在值输入框中输入：`{"message": "Hello World", "timestamp": "2025-01-08"}`
3. 点击"保存"按钮
4. 应该看到成功消息，并且新数据出现在列表中

#### 测试检查存在
1. 在键名输入框中输入：`testKey`
2. 点击"检查存在"按钮
3. 应该看到消息："键 'testKey' 存在"

#### 测试删除功能
1. 找到列表中的任意一项
2. 点击该项右侧的"删除"按钮
3. 确认该项从列表中消失
4. 验证存储项目数量减少

### 步骤 5: 验证数据持久性
1. 保存一些测试数据
2. 关闭应用程序
3. 重新启动应用程序
4. 进入演示页面
5. 验证之前保存的数据是否仍然存在

## 🎯 预期结果

### 界面表现
- ✅ 页面可以正常滚动
- ✅ 滚动条样式美观
- ✅ 返回按钮固定在顶部
- ✅ 内容区域可以完整查看

### 数据表现
- ✅ 应用启动时就有 5 个预设数据项
- ✅ 存储项目数量显示为 5
- ✅ 所有预设数据都可以正常获取和修改
- ✅ 新增数据会立即显示在列表中

### 功能表现
- ✅ 所有按钮都能正常工作
- ✅ 消息提示清晰可见
- ✅ 数据格式化显示正确
- ✅ 错误处理工作正常

## 🐛 如果遇到问题

### 滚动不工作
- 检查浏览器控制台是否有错误
- 尝试刷新页面
- 确认窗口大小足够显示内容

### 预设数据不显示
- 检查应用是否正确启动
- 查看控制台是否有 IPC 通信错误
- 尝试手动刷新页面

### 数据操作失败
- 检查主进程是否正确初始化 electron-store
- 查看控制台错误信息
- 确认 preload 脚本正确加载

## 💡 测试技巧

1. **使用开发者工具**: 按 F12 打开开发者工具查看详细错误信息
2. **测试不同数据类型**: 尝试保存字符串、数字、对象、数组等不同类型的数据
3. **测试边界情况**: 尝试保存空值、特殊字符、大量数据等
4. **验证持久性**: 多次重启应用验证数据是否正确保存

现在您可以充分测试所有功能了！界面应该可以正常滚动，并且您可以看到所有预设的默认数据。
